// TradingViewWidget.jsx
import { marketOverviewWidgetConfig } from "@/lib/constants";
import React, { useEffect, useRef, memo } from "react";

const TradingViewWidget = () => {
	const container = useRef(null);

	return (
		<div
			className="tradingview-widget-container"
			ref={container}
			style={{ height: "100%", width: "100%" }}
		>
			<div
				className="tradingview-widget-container__widget"
				style={{ height: "calc(100% - 32px)", width: "100%" }}
			></div>
			<div className="tradingview-widget-copyright">
				<a
					href="https://www.tradingview.com/symbols/NASDAQ-AAPL/"
					rel="noopener nofollow"
					target="_blank"
				>
					<span className="blue-text">AAPL stock chart</span>
				</a>
				<span className="trademark"> by TradingView</span>
			</div>
		</div>
	);
};

export default memo(TradingViewWidget);
